jQuery(document).ready(function($) {
    'use strict';
    
    // Inicjalizacja sortowania
    $('#pco-categories-sortable').sortable({
        handle: '.pco-category-drag-handle',
        placeholder: 'pco-category-placeholder',
        update: function(event, ui) {
            updateCategoriesOrder();
        }
    });
    
    // Dodawanie nowej kategorii
    $('#pco-add-category-form').on('submit', function(e) {
        e.preventDefault();
        
        var formData = {
            action: 'pco_save_category',
            nonce: pco_categories_admin.nonce,
            category_id: $('#category_id').val(),
            category_name: $('#category_name').val(),
            category_enabled: $('#category_enabled').is(':checked') ? '1' : '0'
        };
        
        $.post(pco_categories_admin.ajax_url, formData)
            .done(function(response) {
                if (response.success) {
                    showNotice(response.data.message, 'success');
                    location.reload(); // Odświeżenie strony po dodaniu
                } else {
                    showNotice(response.data.message, 'error');
                }
            })
            .fail(function() {
                showNotice(pco_categories_admin.strings.error_occurred, 'error');
            });
    });
    
    // Edycja kategorii
    $(document).on('click', '.pco-edit-category', function() {
        var categoryId = $(this).data('id');
        var categoryItem = $(this).closest('.pco-category-item');
        var categoryName = categoryItem.find('.pco-category-header strong').text();
        var isEnabled = categoryItem.find('.pco-category-status').hasClass('enabled');
        
        // Wypełnienie formularza edycji
        $('#edit_category_id').val(categoryId);
        $('#edit_category_name').val(categoryName);
        $('#edit_category_enabled').prop('checked', isEnabled);
        
        // Pokazanie modala
        $('#pco-edit-category-modal').show();
    });
    
    // Zapisanie edycji kategorii
    $('#pco-edit-category-form').on('submit', function(e) {
        e.preventDefault();
        
        var formData = {
            action: 'pco_save_category',
            nonce: pco_categories_admin.nonce,
            category_id: $('#edit_category_id').val(),
            category_name: $('#edit_category_name').val(),
            category_enabled: $('#edit_category_enabled').is(':checked') ? '1' : '0',
            is_edit: '1'
        };
        
        $.post(pco_categories_admin.ajax_url, formData)
            .done(function(response) {
                if (response.success) {
                    showNotice(response.data.message, 'success');
                    $('#pco-edit-category-modal').hide();
                    location.reload(); // Odświeżenie strony po edycji
                } else {
                    showNotice(response.data.message, 'error');
                }
            })
            .fail(function() {
                showNotice(pco_categories_admin.strings.error_occurred, 'error');
            });
    });
    
    // Usuwanie kategorii
    $(document).on('click', '.pco-delete-category', function() {
        if (!confirm(pco_categories_admin.strings.confirm_delete)) {
            return;
        }
        
        var categoryId = $(this).data('id');
        var categoryItem = $(this).closest('.pco-category-item');
        
        var formData = {
            action: 'pco_delete_category',
            nonce: pco_categories_admin.nonce,
            category_id: categoryId
        };
        
        $.post(pco_categories_admin.ajax_url, formData)
            .done(function(response) {
                if (response.success) {
                    showNotice(response.data.message, 'success');
                    categoryItem.fadeOut(300, function() {
                        $(this).remove();
                        updateCategoriesOrder();
                    });
                } else {
                    showNotice(response.data.message, 'error');
                }
            })
            .fail(function() {
                showNotice(pco_categories_admin.strings.error_occurred, 'error');
            });
    });
    
    // Zamykanie modala
    $('.pco-modal-close').on('click', function() {
        $('#pco-edit-category-modal').hide();
    });
    
    // Zamykanie modala po kliknięciu w tło
    $('#pco-edit-category-modal').on('click', function(e) {
        if (e.target === this) {
            $(this).hide();
        }
    });
    
    // Aktualizacja kolejności kategorii
    function updateCategoriesOrder() {
        var order = [];
        $('#pco-categories-sortable .pco-category-item').each(function() {
            order.push($(this).data('id'));
        });
        
        if (order.length === 0) {
            return;
        }
        
        var formData = {
            action: 'pco_update_categories_order',
            nonce: pco_categories_admin.nonce,
            order: order
        };
        
        $.post(pco_categories_admin.ajax_url, formData)
            .done(function(response) {
                if (response.success) {
                    // Opcjonalnie można pokazać powiadomienie o aktualizacji kolejności
                    // showNotice(response.data.message, 'success');
                } else {
                    showNotice(response.data.message, 'error');
                }
            })
            .fail(function() {
                showNotice(pco_categories_admin.strings.error_occurred, 'error');
            });
    }
    
    // Pokazywanie powiadomień
    function showNotice(message, type) {
        var noticeClass = type === 'success' ? 'notice-success' : 'notice-error';
        var notice = $('<div class="notice ' + noticeClass + ' is-dismissible"><p>' + message + '</p></div>');
        
        $('#pco-categories-notices').html(notice);
        
        // Automatyczne ukrycie po 5 sekundach
        setTimeout(function() {
            notice.fadeOut();
        }, 5000);
        
        // Przewinięcie do góry strony
        $('html, body').animate({
            scrollTop: 0
        }, 300);
    }
    
    // Walidacja ID kategorii w czasie rzeczywistym
    $('#category_id').on('input', function() {
        var value = $(this).val();
        var validValue = value.toLowerCase().replace(/[^a-z0-9_]/g, '');
        
        if (value !== validValue) {
            $(this).val(validValue);
        }
    });
    
    // Automatyczne generowanie ID na podstawie nazwy
    $('#category_name').on('input', function() {
        var name = $(this).val();
        var id = name.toLowerCase()
            .replace(/ą/g, 'a')
            .replace(/ć/g, 'c')
            .replace(/ę/g, 'e')
            .replace(/ł/g, 'l')
            .replace(/ń/g, 'n')
            .replace(/ó/g, 'o')
            .replace(/ś/g, 's')
            .replace(/ź/g, 'z')
            .replace(/ż/g, 'z')
            .replace(/[^a-z0-9]/g, '_')
            .replace(/_+/g, '_')
            .replace(/^_|_$/g, '');
        
        // Tylko jeśli pole ID jest puste
        if ($('#category_id').val() === '') {
            $('#category_id').val(id);
        }
    });
});
