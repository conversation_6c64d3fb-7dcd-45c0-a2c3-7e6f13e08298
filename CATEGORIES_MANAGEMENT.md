# System zarządzania kategoriami opcji produktów

## Przegląd

Wtyczka Papierotka Custom Order została rozszerzona o dynamiczny system zarządzania kategoriami opcji produktów. Zamiast hardkodowanych kategorii w kodzie, teraz można łatwo dodawać, ed<PERSON><PERSON><PERSON> i usuwać kategorie przez panel administracyjny WordPress.

## Funkcjonalności

### ✅ **Zarządzanie kategoriami**
- Dodawanie nowych kategorii opcji
- Edycja nazw istniejących kategorii
- Włączanie/wyłączanie kategorii
- Usuwanie niepotrzebnych kategorii
- Zmiana kole<PERSON> wyświetlania (przeciągnij i upuść)

### ✅ **Panel administracyjny**
- Intuicyjny interfejs w panelu WordPress
- Lokalizacja: **WooCommerce → Kategorie opcji**
- Responsywny design dostosowany do różnych urządzeń
- Walida<PERSON>ja danych w czasie rzeczywistym

### ✅ **Kompatybilność wsteczna**
- Istniejące produkty będą działać bez zmian
- Automatyczna migracja z hardkodowanych kategorii
- Zachowanie wszystkich ustawień produktów

## Jak używać

### **Dostęp do panelu zarządzania**
1. Zaloguj się do panelu administracyjnego WordPress
2. Przejdź do **WooCommerce → Kategorie opcji**
3. Zarządzaj kategoriami za pomocą interfejsu

### **Dodawanie nowej kategorii**
1. W sekcji "Dodaj nową kategorię" wypełnij:
   - **ID kategorii**: Unikalny identyfikator (tylko małe litery, cyfry, podkreślniki)
   - **Nazwa kategorii**: Nazwa wyświetlana użytkownikom
   - **Status**: Czy kategoria ma być włączona
2. Kliknij "Dodaj kategorię"

### **Edycja kategorii**
1. W sekcji "Istniejące kategorie" kliknij "Edytuj" przy wybranej kategorii
2. Zmień nazwę lub status w oknie modalnym
3. Kliknij "Zapisz zmiany"

### **Zmiana kolejności**
1. Przeciągnij kategorie w sekcji "Istniejące kategorie"
2. Kolejność zostanie automatycznie zapisana

### **Usuwanie kategorii**
1. Kliknij "Usuń" przy wybranej kategorii
2. Potwierdź usunięcie w oknie dialogowym

## Struktura techniczna

### **Nowe pliki**
```
includes/
├── class-categories-manager.php     # Główna klasa zarządzająca
admin/
├── class-categories-admin.php       # Panel administracyjny
assets/
├── js/categories-admin.js           # JavaScript dla panelu admin
├── css/categories-admin.css         # Style dla panelu admin
```

### **Zmodyfikowane pliki**
- `papierotka-custom-order.php` - Rejestracja nowych klas
- `includes/class-product-fields.php` - Dynamiczne pobieranie kategorii
- `includes/class-direct-order-form.php` - Aktualizacja nazw kategorii
- `assets/js/papierotka-custom-order.js` - Dynamiczne ładowanie kategorii

### **Baza danych**
Kategorie są przechowywane jako opcja WordPress: `pco_product_categories`

Struktura danych:
```php
array(
    'category_id' => array(
        'name' => 'Nazwa kategorii',
        'enabled' => true,
        'order' => 1
    )
)
```

## API dla deweloperów

### **Pobieranie instancji managera**
```php
$categories_manager = PCO_Categories_Manager::get_instance();
```

### **Podstawowe operacje**
```php
// Pobieranie wszystkich kategorii
$categories = $categories_manager->get_categories();

// Pobieranie tylko włączonych kategorii
$enabled_categories = $categories_manager->get_categories(true);

// Pobieranie kategorii jako tablicy (kompatybilność wsteczna)
$categories_array = $categories_manager->get_categories_as_array(true);

// Dodawanie nowej kategorii
$result = $categories_manager->add_category('nowa_kategoria', 'Nowa Kategoria', true);

// Aktualizacja kategorii
$result = $categories_manager->update_category('kategoria_id', array(
    'name' => 'Nowa nazwa',
    'enabled' => false
));

// Usuwanie kategorii
$result = $categories_manager->delete_category('kategoria_id');

// Sprawdzanie czy kategoria istnieje
$exists = $categories_manager->category_exists('kategoria_id');

// Pobieranie nazwy kategorii
$name = $categories_manager->get_category_name('kategoria_id');
```

### **AJAX Endpoints**
- `pco_get_categories` - Pobieranie kategorii
- `pco_save_category` - Zapisywanie kategorii
- `pco_delete_category` - Usuwanie kategorii
- `pco_update_categories_order` - Aktualizacja kolejności

## Migracja z poprzedniej wersji

System automatycznie wykrywa, czy to pierwsza instalacja. Jeśli tak, nie tworzy żadnych domyślnych kategorii - użytkownik może dodać swoje własne.

Istniejące produkty z konfiguracją kategorii będą nadal działać, ale nowe kategorie trzeba będzie dodać przez panel administracyjny.

## Bezpieczeństwo

- Wszystkie operacje wymagają uprawnień `manage_woocommerce`
- Walidacja i sanityzacja wszystkich danych wejściowych
- Nonce verification dla wszystkich operacji AJAX
- Walidacja ID kategorii (tylko dozwolone znaki)

## Rozwiązywanie problemów

### **Kategorie nie wyświetlają się na stronie produktu**
1. Sprawdź czy kategorie są włączone w panelu zarządzania
2. Upewnij się, że produkt ma włączony niestandardowy formularz
3. Sprawdź czy kategorie mają przypisane opcje

### **Błędy JavaScript**
1. Sprawdź konsolę przeglądarki
2. Upewnij się, że skrypty są poprawnie załadowane
3. Sprawdź czy `window.pco_category_names` jest dostępne

### **Problemy z zapisywaniem**
1. Sprawdź uprawnienia użytkownika
2. Sprawdź logi błędów WordPress
3. Upewnij się, że AJAX endpoints są poprawnie zarejestrowane

## Wsparcie

W przypadku problemów sprawdź:
1. Logi błędów WordPress
2. Konsolę przeglądarki
3. Uprawnienia użytkownika
4. Kompatybilność z innymi wtyczkami
