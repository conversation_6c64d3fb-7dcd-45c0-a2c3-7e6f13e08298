<?php
/**
 * Główna klasa zarządzająca menu administracyjnym wtyczki Papierotka Custom Order
 */

if (!defined('ABSPATH')) {
    exit;
}

class PCO_Main_Admin {

    /**
     * Konstruktor
     */
    public function __construct() {
        // Dodanie głównego menu wtyczki
        add_action('admin_menu', array($this, 'add_main_menu'), 5);

        // Ukrycie starych menu z WooCommerce (wyższy priorytet)
        add_action('admin_menu', array($this, 'hide_old_menus'), 999);

        // Dodanie skryptów i stylów dla panelu administracyjnego
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));

        // Obsługa AJAX dla dashboard
        add_action('wp_ajax_pco_get_dashboard_stats', array($this, 'ajax_get_dashboard_stats'));
    }

    /**
     * Dodanie głównego menu wtyczki
     */
    public function add_main_menu() {
        // Główne menu Papierotka
        add_menu_page(
            __('Papierotka Custom Order', 'papierotka-custom-order'),
            __('Papierotka', 'papierotka-custom-order'),
            'manage_woocommerce',
            'papierotka-admin',
            array($this, 'dashboard_page'),
            'dashicons-clipboard',
            30
        );

        // Dashboard jako pierwsze podmenu (ukrywa duplikat głównego menu)
        add_submenu_page(
            'papierotka-admin',
            __('Dashboard - Papierotka', 'papierotka-custom-order'),
            __('Dashboard', 'papierotka-custom-order'),
            'manage_woocommerce',
            'papierotka-admin',
            array($this, 'dashboard_page')
        );

        // Sekcja Konfiguracja (separator)
        add_submenu_page(
            'papierotka-admin',
            '',
            '<span style="color: #999; font-style: italic;">— ' . __('Konfiguracja', 'papierotka-custom-order') . ' —</span>',
            'manage_woocommerce',
            '#',
            ''
        );
    }

    /**
     * Strona Dashboard
     */
    public function dashboard_page() {
        // Sprawdzenie uprawnień
        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('Nie masz uprawnień do tej strony.', 'papierotka-custom-order'));
        }

        include_once PCO_PLUGIN_DIR . 'admin/templates/dashboard.php';
    }

    /**
     * Ukrycie starych menu z WooCommerce
     */
    public function hide_old_menus() {
        global $submenu;

        // Usunięcie starych podmenu z WooCommerce
        if (isset($submenu['woocommerce'])) {
            foreach ($submenu['woocommerce'] as $key => $menu_item) {
                // Sprawdzenie czy to nasze stare menu
                if (in_array($menu_item[2], array(
                    'pco-orders',
                    'pco-email-settings',
                    'pco-form-builder',
                    'pco-categories',
                    'pco-debug'
                ))) {
                    unset($submenu['woocommerce'][$key]);
                }
            }
        }
    }

    /**
     * Dodanie skryptów i stylów dla panelu administracyjnego
     */
    public function enqueue_admin_scripts($hook) {
        // Ładowanie tylko na stronach wtyczki
        if (strpos($hook, 'papierotka') === false) {
            return;
        }

        // Style dla dashboard
        wp_enqueue_style(
            'pco-admin-dashboard',
            PCO_PLUGIN_URL . 'assets/css/admin-dashboard.css',
            array(),
            PCO_VERSION
        );

        // JavaScript dla dashboard
        wp_enqueue_script(
            'pco-admin-dashboard',
            PCO_PLUGIN_URL . 'assets/js/admin-dashboard.js',
            array('jquery'),
            PCO_VERSION,
            true
        );

        // Przekazanie danych do skryptu
        wp_localize_script('pco-admin-dashboard', 'pco_admin_data', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('pco-admin-nonce'),
            'strings' => array(
                'loading' => __('Ładowanie...', 'papierotka-custom-order'),
                'error' => __('Wystąpił błąd podczas ładowania danych.', 'papierotka-custom-order')
            )
        ));
    }

    /**
     * AJAX - Pobieranie statystyk dla dashboard
     */
    public function ajax_get_dashboard_stats() {
        // Sprawdzenie nonce
        if (!wp_verify_nonce($_POST['nonce'], 'pco-admin-nonce')) {
            wp_die(__('Błąd bezpieczeństwa.', 'papierotka-custom-order'));
        }

        // Sprawdzenie uprawnień
        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('Nie masz uprawnień do tej akcji.', 'papierotka-custom-order'));
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'pco_orders';

        // Sprawdzenie czy tabela istnieje
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
            wp_send_json_error(array(
                'message' => __('Tabela zamówień nie istnieje.', 'papierotka-custom-order')
            ));
        }

        // Pobieranie statystyk
        $today = date('Y-m-d');
        $week_start = date('Y-m-d', strtotime('monday this week'));
        $month_start = date('Y-m-01');

        $stats = array(
            'today' => $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM $table_name WHERE DATE(created_at) = %s",
                $today
            )),
            'week' => $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM $table_name WHERE DATE(created_at) >= %s",
                $week_start
            )),
            'month' => $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM $table_name WHERE DATE(created_at) >= %s",
                $month_start
            )),
            'total' => $wpdb->get_var("SELECT COUNT(*) FROM $table_name")
        );

        // Ostatnie zamówienia
        $recent_orders = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $table_name ORDER BY created_at DESC LIMIT %d",
            5
        ));

        wp_send_json_success(array(
            'stats' => $stats,
            'recent_orders' => $recent_orders
        ));
    }
}
