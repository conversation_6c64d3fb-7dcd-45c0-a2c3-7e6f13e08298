/**
 * JavaScript dla Dashboard wtyczki Papierotka Custom Order
 */

jQuery(document).ready(function($) {

    // Ładowanie statystyk i ostatnich zamówień
    loadDashboardData();

    /**
     * Ładowanie danych dashboard
     */
    function loadDashboardData() {
        $.ajax({
            url: pco_admin_data.ajax_url,
            type: 'POST',
            data: {
                action: 'pco_get_dashboard_stats',
                nonce: pco_admin_data.nonce
            },
            success: function(response) {
                if (response.success) {
                    updateStats(response.data.stats);
                    updateRecentOrders(response.data.recent_orders);
                } else {
                    showError(response.data.message || pco_admin_data.strings.error);
                }
            },
            error: function() {
                showError(pco_admin_data.strings.error);
            }
        });
    }

    /**
     * Aktualizacja statystyk
     */
    function updateStats(stats) {
        $('#stat-today').text(stats.today || 0);
        $('#stat-week').text(stats.week || 0);
        $('#stat-month').text(stats.month || 0);
        $('#stat-total').text(stats.total || 0);

        // Animacja liczb
        animateNumbers();
    }

    /**
     * Aktualizacja ostatnich zamówień
     */
    function updateRecentOrders(orders) {
        const $tbody = $('#recent-orders-tbody');
        const $loading = $('#recent-orders-loading');
        const $content = $('#recent-orders-content');
        const $error = $('#recent-orders-error');

        $loading.hide();

        if (!orders || orders.length === 0) {
            $tbody.html('<tr><td colspan="6" style="text-align: center; padding: 40px; color: #666;">' +
                'Brak zamówień do wyświetlenia.' +
                '</td></tr>');
        } else {
            let html = '';
            orders.forEach(function(order) {
                // Parsowanie danych formularza z kolumny form_data
                let formData = {};
                try {
                    formData = JSON.parse(order.form_data || '{}');
                } catch (e) {
                    formData = {};
                }

                // Pobranie danych klienta z różnych możliwych pól
                const customerName = formData.name || formData.customer_name || formData.imie_nazwisko || 'Brak danych';
                const customerEmail = formData.email || formData.customer_email || 'Brak danych';
                const status = getStatusLabel(order.status || 'new');
                const createdAt = formatDate(order.created_at);

                html += '<tr>';
                html += '<td><strong>#' + order.order_id + '</strong></td>';
                html += '<td>' + escapeHtml(customerName) + '</td>';
                html += '<td>' + escapeHtml(customerEmail) + '</td>';
                html += '<td>' + status + '</td>';
                html += '<td>' + createdAt + '</td>';
                html += '<td>';
                html += '<a href="' + getOrderDetailsUrl(order.order_id) + '" class="button button-small">';
                html += 'Szczegóły';
                html += '</a>';
                html += '</td>';
                html += '</tr>';
            });
            $tbody.html(html);
        }

        $content.show();
    }

    /**
     * Wyświetlenie błędu
     */
    function showError(message) {
        $('#recent-orders-loading').hide();
        $('#recent-orders-error').show().find('p').text(message);

        // Ustawienie domyślnych wartości dla statystyk
        $('#stat-today, #stat-week, #stat-month, #stat-total').text('0');
    }

    /**
     * Animacja liczb w statystykach
     */
    function animateNumbers() {
        $('.pco-stat-number').each(function() {
            const $this = $(this);
            const finalValue = parseInt($this.text()) || 0;

            if (finalValue === 0) return;

            $this.text('0');

            $({ counter: 0 }).animate({ counter: finalValue }, {
                duration: 1000,
                easing: 'swing',
                step: function() {
                    $this.text(Math.ceil(this.counter));
                },
                complete: function() {
                    $this.text(finalValue);
                }
            });
        });
    }

    /**
     * Formatowanie daty
     */
    function formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffTime = Math.abs(now - date);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays === 1) {
            return 'Dzisiaj';
        } else if (diffDays === 2) {
            return 'Wczoraj';
        } else if (diffDays <= 7) {
            return diffDays + ' dni temu';
        } else {
            return date.toLocaleDateString('pl-PL', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }
    }

    /**
     * Pobieranie etykiety statusu
     */
    function getStatusLabel(status) {
        const statusLabels = {
            'pending': 'Oczekujące',
            'processing': 'W realizacji',
            'completed': 'Zakończone',
            'cancelled': 'Anulowane'
        };

        const label = statusLabels[status] || 'Nieznany';
        return '<span class="pco-order-status ' + status + '">' + label + '</span>';
    }

    /**
     * Pobieranie URL do szczegółów zamówienia
     */
    function getOrderDetailsUrl(orderId) {
        return pco_admin_data.ajax_url.replace('admin-ajax.php', 'admin.php?page=pco-orders&search=' + orderId);
    }

    /**
     * Escape HTML
     */
    function escapeHtml(text) {
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, function(m) { return map[m]; });
    }

    /**
     * Odświeżanie danych co 5 minut
     */
    setInterval(function() {
        loadDashboardData();
    }, 300000); // 5 minut

    /**
     * Obsługa kliknięcia w kartę statystyk
     */
    $('.pco-stat-card').on('click', function() {
        // Przekierowanie do strony zamówień z odpowiednim filtrem
        const statType = $(this).find('.pco-stat-number').attr('id').replace('stat-', '');
        let filterUrl = pco_admin_data.ajax_url.replace('admin-ajax.php', 'admin.php?page=pco-orders');

        switch(statType) {
            case 'today':
                filterUrl += '&filter=today';
                break;
            case 'week':
                filterUrl += '&filter=week';
                break;
            case 'month':
                filterUrl += '&filter=month';
                break;
        }

        window.location.href = filterUrl;
    });

    /**
     * Tooltip dla kart statystyk
     */
    $('.pco-stat-card').attr('title', 'Kliknij aby przejść do filtrowanych zamówień');

    /**
     * Efekt hover dla szybkich linków
     */
    $('.pco-quick-link').on('mouseenter', function() {
        $(this).find('.dashicons').addClass('animated');
    }).on('mouseleave', function() {
        $(this).find('.dashicons').removeClass('animated');
    });

});

/**
 * Style CSS dla animacji (dodawane dynamicznie)
 */
jQuery(document).ready(function($) {
    const animationCSS = `
        <style>
        .dashicons.animated {
            animation: pulse 0.5s ease-in-out;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .pco-stat-card {
            cursor: pointer;
        }

        .pco-stat-card:hover .pco-stat-icon {
            background: #005a87;
        }
        </style>
    `;

    $('head').append(animationCSS);
});
