<?php
/**
 * Klasa zarządzająca kategoriami opcji produktów
 */
class PCO_Categories_Manager {

    private static $instance = null;
    private $option_name = 'pco_product_categories';

    /**
     * Konstruktor
     */
    public function __construct() {
        // Ustawienie instancji singleton
        if (self::$instance === null) {
            self::$instance = $this;
        }

        // Inicjalizacja domyślnych kategorii przy pierwszym uruchomieniu
        add_action('init', array($this, 'maybe_initialize_default_categories'));

        // AJAX endpoints
        add_action('wp_ajax_pco_get_categories', array($this, 'ajax_get_categories'));
        add_action('wp_ajax_nopriv_pco_get_categories', array($this, 'ajax_get_categories'));
    }

    /**
     * Singleton pattern
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Inicjalizacja domyślnych kategorii (tylko przy pierwszym uruchomieniu)
     */
    public function maybe_initialize_default_categories() {
        $categories = get_option($this->option_name, false);

        // Jeśli opcja nie istnieje, nie inicjalizujemy żadnych domyślnych kategorii
        // Użytkownik będzie mógł dodać swoje własne kategorie
        if ($categories === false) {
            update_option($this->option_name, array());
        }
    }

    /**
     * Pobieranie wszystkich kategorii
     */
    public function get_categories($enabled_only = false) {
        $categories = get_option($this->option_name, array());

        if ($enabled_only) {
            return array_filter($categories, function($category) {
                return isset($category['enabled']) && $category['enabled'];
            });
        }

        return $categories;
    }

    /**
     * Pobieranie pojedynczej kategorii
     */
    public function get_category($category_id) {
        $categories = $this->get_categories();
        return isset($categories[$category_id]) ? $categories[$category_id] : null;
    }

    /**
     * Pobieranie nazwy kategorii
     */
    public function get_category_name($category_id) {
        $category = $this->get_category($category_id);
        return $category ? $category['name'] : $category_id;
    }

    /**
     * Dodawanie nowej kategorii
     */
    public function add_category($category_id, $name, $enabled = true, $order = null) {
        $categories = $this->get_categories();

        // Sprawdzenie czy kategoria już istnieje
        if (isset($categories[$category_id])) {
            return new WP_Error('category_exists', __('Kategoria o tym ID już istnieje', 'papierotka-custom-order'));
        }

        // Walidacja ID kategorii (tylko litery, cyfry, podkreślniki)
        if (!preg_match('/^[a-z0-9_]+$/', $category_id)) {
            return new WP_Error('invalid_id', __('ID kategorii może zawierać tylko małe litery, cyfry i podkreślniki', 'papierotka-custom-order'));
        }

        // Ustalenie kolejności
        if ($order === null) {
            $order = count($categories) + 1;
        }

        $categories[$category_id] = array(
            'name' => sanitize_text_field($name),
            'enabled' => (bool) $enabled,
            'order' => (int) $order
        );

        // Sortowanie według kolejności
        uasort($categories, function($a, $b) {
            return $a['order'] - $b['order'];
        });

        return update_option($this->option_name, $categories);
    }

    /**
     * Aktualizacja kategorii
     */
    public function update_category($category_id, $data) {
        $categories = $this->get_categories();

        if (!isset($categories[$category_id])) {
            return new WP_Error('category_not_found', __('Kategoria nie została znaleziona', 'papierotka-custom-order'));
        }

        // Aktualizacja danych
        if (isset($data['name'])) {
            $categories[$category_id]['name'] = sanitize_text_field($data['name']);
        }

        if (isset($data['enabled'])) {
            $categories[$category_id]['enabled'] = (bool) $data['enabled'];
        }

        if (isset($data['order'])) {
            $categories[$category_id]['order'] = (int) $data['order'];
        }

        // Sortowanie według kolejności
        uasort($categories, function($a, $b) {
            return $a['order'] - $b['order'];
        });

        return update_option($this->option_name, $categories);
    }

    /**
     * Usuwanie kategorii
     */
    public function delete_category($category_id) {
        $categories = $this->get_categories();

        if (!isset($categories[$category_id])) {
            return new WP_Error('category_not_found', __('Kategoria nie została znaleziona', 'papierotka-custom-order'));
        }

        unset($categories[$category_id]);

        return update_option($this->option_name, $categories);
    }

    /**
     * Aktualizacja kolejności kategorii
     */
    public function update_categories_order($order_data) {
        $categories = $this->get_categories();

        foreach ($order_data as $category_id => $order) {
            if (isset($categories[$category_id])) {
                $categories[$category_id]['order'] = (int) $order;
            }
        }

        // Sortowanie według kolejności
        uasort($categories, function($a, $b) {
            return $a['order'] - $b['order'];
        });

        return update_option($this->option_name, $categories);
    }

    /**
     * Pobieranie kategorii posortowanych według kolejności
     */
    public function get_categories_ordered($enabled_only = false) {
        $categories = $this->get_categories($enabled_only);

        // Sortowanie według kolejności
        uasort($categories, function($a, $b) {
            return $a['order'] - $b['order'];
        });

        return $categories;
    }

    /**
     * AJAX endpoint do pobierania kategorii
     */
    public function ajax_get_categories() {
        $enabled_only = isset($_GET['enabled_only']) && $_GET['enabled_only'] === 'true';
        $categories = $this->get_categories_ordered($enabled_only);

        wp_send_json_success($categories);
    }

    /**
     * Sprawdzenie czy kategoria istnieje
     */
    public function category_exists($category_id) {
        $categories = $this->get_categories();
        return isset($categories[$category_id]);
    }

    /**
     * Sprawdzenie czy kategoria jest włączona
     */
    public function is_category_enabled($category_id) {
        $category = $this->get_category($category_id);
        return $category && isset($category['enabled']) && $category['enabled'];
    }

    /**
     * Pobieranie listy ID kategorii w kolejności
     */
    public function get_category_ids_ordered($enabled_only = false) {
        $categories = $this->get_categories_ordered($enabled_only);
        return array_keys($categories);
    }

    /**
     * Eksport kategorii do tablicy (dla kompatybilności wstecznej)
     */
    public function get_categories_as_array($enabled_only = false) {
        $categories = $this->get_categories_ordered($enabled_only);
        $result = array();

        foreach ($categories as $id => $category) {
            $result[$id] = $category['name'];
        }

        return $result;
    }
}
