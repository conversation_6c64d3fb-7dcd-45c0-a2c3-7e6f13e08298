/* Panel administracyjny kate<PERSON>ii */
.pco-categories-container {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 30px;
    margin-top: 20px;
}

.pco-categories-form {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.pco-categories-form h2 {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 1.3em;
    color: #23282d;
}

.pco-categories-list {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.pco-categories-list h2 {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 1.3em;
    color: #23282d;
}

/* Siatka kategorii */
.pco-categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.pco-category-item {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 15px;
    cursor: move;
    transition: all 0.2s ease;
    position: relative;
}

.pco-category-item:hover {
    background: #f0f0f0;
    border-color: #999;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.pco-category-item.ui-sortable-helper {
    transform: rotate(2deg);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    z-index: 1000;
}

.pco-category-placeholder {
    background: #e0e0e0;
    border: 2px dashed #999;
    border-radius: 6px;
    height: 120px;
    margin: 5px 0;
}

/* Nagłówek kategorii */
.pco-category-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    gap: 10px;
}

.pco-category-drag-handle {
    font-size: 16px;
    color: #666;
    cursor: grab;
    user-select: none;
    line-height: 1;
}

.pco-category-drag-handle:active {
    cursor: grabbing;
}

.pco-category-header strong {
    flex: 1;
    font-size: 14px;
    color: #23282d;
}

.pco-category-status {
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.pco-category-status.enabled {
    background: #d4edda;
    color: #155724;
}

.pco-category-status.disabled {
    background: #f8d7da;
    color: #721c24;
}

/* Szczegóły kategorii */
.pco-category-details {
    margin-bottom: 15px;
}

.pco-category-details p {
    margin: 5px 0;
    font-size: 12px;
    color: #666;
}

/* Akcje kategorii */
.pco-category-actions {
    display: flex;
    gap: 8px;
}

.pco-category-actions .button {
    font-size: 11px;
    height: auto;
    padding: 4px 8px;
    line-height: 1.4;
}

/* Modal */
.pco-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pco-modal-content {
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.pco-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 20px 0;
    border-bottom: 1px solid #ddd;
    margin-bottom: 20px;
}

.pco-modal-header h3 {
    margin: 0;
    font-size: 1.2em;
    color: #23282d;
}

.pco-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #666;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pco-modal-close:hover {
    color: #000;
}

.pco-modal form {
    padding: 0 20px;
}

.pco-modal-footer {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    padding: 20px;
    border-top: 1px solid #ddd;
    margin-top: 20px;
}

/* Powiadomienia */
#pco-categories-notices {
    margin-bottom: 20px;
}

#pco-categories-notices .notice {
    margin: 5px 0 15px 0;
}

/* Responsywność */
@media (max-width: 1200px) {
    .pco-categories-container {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .pco-categories-grid {
        grid-template-columns: 1fr;
    }
    
    .pco-category-header {
        flex-wrap: wrap;
    }
    
    .pco-category-status {
        order: 3;
        flex-basis: 100%;
        margin-top: 5px;
        text-align: center;
    }
    
    .pco-modal-content {
        width: 95%;
        margin: 20px;
    }
}

/* Animacje */
.pco-category-item {
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Stany formularza */
.form-table input[type="text"]:focus {
    border-color: #007cba;
    box-shadow: 0 0 0 1px #007cba;
}

.form-table .description {
    font-style: italic;
    color: #666;
    margin-top: 5px;
}

/* Sortowanie */
.ui-sortable-helper {
    opacity: 0.8;
}

.ui-sortable-placeholder {
    visibility: visible !important;
}

/* Dodatkowe style dla lepszej UX */
.pco-categories-form .submit {
    margin-top: 0;
    padding-top: 10px;
    border-top: 1px solid #ddd;
}

.pco-category-item:last-child {
    margin-bottom: 0;
}
