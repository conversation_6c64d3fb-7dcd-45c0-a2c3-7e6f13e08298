<?php
/**
 * <PERSON>lasa obsługująca panel administracyjny kategorii
 */
class PCO_Categories_Admin {
    
    private $categories_manager;
    
    /**
     * Konstruktor
     */
    public function __construct() {
        $this->categories_manager = PCO_Categories_Manager::get_instance();
        
        // Dodanie menu w panelu administracyjnym
        add_action('admin_menu', array($this, 'add_admin_menu'));
        
        // Rejestracja skryptów i stylów
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        
        // AJAX endpoints
        add_action('wp_ajax_pco_save_category', array($this, 'ajax_save_category'));
        add_action('wp_ajax_pco_delete_category', array($this, 'ajax_delete_category'));
        add_action('wp_ajax_pco_update_categories_order', array($this, 'ajax_update_categories_order'));
    }
    
    /**
     * Dodanie menu w panelu administracyjnym
     */
    public function add_admin_menu() {
        add_submenu_page(
            'woocommerce',
            __('Kategorie opcji produktów', 'papierotka-custom-order'),
            __('Kategorie opcji', 'papierotka-custom-order'),
            'manage_woocommerce',
            'pco-categories',
            array($this, 'admin_page')
        );
    }
    
    /**
     * Rejestracja skryptów i stylów dla panelu administracyjnego
     */
    public function enqueue_admin_scripts($hook) {
        if ($hook !== 'woocommerce_page_pco-categories') {
            return;
        }
        
        wp_enqueue_script('jquery-ui-sortable');
        wp_enqueue_script(
            'pco-categories-admin',
            PCO_PLUGIN_URL . 'assets/js/categories-admin.js',
            array('jquery', 'jquery-ui-sortable'),
            PCO_VERSION,
            true
        );
        
        wp_enqueue_style(
            'pco-categories-admin',
            PCO_PLUGIN_URL . 'assets/css/categories-admin.css',
            array(),
            PCO_VERSION
        );
        
        // Przekazanie danych do JavaScript
        wp_localize_script('pco-categories-admin', 'pco_categories_admin', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('pco_categories_nonce'),
            'strings' => array(
                'confirm_delete' => __('Czy na pewno chcesz usunąć tę kategorię?', 'papierotka-custom-order'),
                'error_occurred' => __('Wystąpił błąd. Spróbuj ponownie.', 'papierotka-custom-order'),
                'category_saved' => __('Kategoria została zapisana.', 'papierotka-custom-order'),
                'category_deleted' => __('Kategoria została usunięta.', 'papierotka-custom-order'),
                'order_updated' => __('Kolejność kategorii została zaktualizowana.', 'papierotka-custom-order')
            )
        ));
    }
    
    /**
     * Strona administracyjna
     */
    public function admin_page() {
        $categories = $this->categories_manager->get_categories_ordered();
        ?>
        <div class="wrap">
            <h1><?php _e('Kategorie opcji produktów', 'papierotka-custom-order'); ?></h1>
            
            <div id="pco-categories-notices"></div>
            
            <div class="pco-categories-container">
                <div class="pco-categories-form">
                    <h2><?php _e('Dodaj nową kategorię', 'papierotka-custom-order'); ?></h2>
                    <form id="pco-add-category-form">
                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label for="category_id"><?php _e('ID kategorii', 'papierotka-custom-order'); ?></label>
                                </th>
                                <td>
                                    <input type="text" id="category_id" name="category_id" class="regular-text" required>
                                    <p class="description"><?php _e('Tylko małe litery, cyfry i podkreślniki (np. koperta, personalizacja_koperty)', 'papierotka-custom-order'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="category_name"><?php _e('Nazwa kategorii', 'papierotka-custom-order'); ?></label>
                                </th>
                                <td>
                                    <input type="text" id="category_name" name="category_name" class="regular-text" required>
                                    <p class="description"><?php _e('Nazwa wyświetlana użytkownikom', 'papierotka-custom-order'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="category_enabled"><?php _e('Status', 'papierotka-custom-order'); ?></label>
                                </th>
                                <td>
                                    <label>
                                        <input type="checkbox" id="category_enabled" name="category_enabled" value="1" checked>
                                        <?php _e('Kategoria włączona', 'papierotka-custom-order'); ?>
                                    </label>
                                </td>
                            </tr>
                        </table>
                        
                        <?php wp_nonce_field('pco_categories_nonce', 'pco_categories_nonce'); ?>
                        
                        <p class="submit">
                            <button type="submit" class="button button-primary"><?php _e('Dodaj kategorię', 'papierotka-custom-order'); ?></button>
                        </p>
                    </form>
                </div>
                
                <div class="pco-categories-list">
                    <h2><?php _e('Istniejące kategorie', 'papierotka-custom-order'); ?></h2>
                    
                    <?php if (empty($categories)): ?>
                        <p><?php _e('Brak kategorii. Dodaj pierwszą kategorię używając formularza powyżej.', 'papierotka-custom-order'); ?></p>
                    <?php else: ?>
                        <p class="description"><?php _e('Przeciągnij kategorie, aby zmienić ich kolejność.', 'papierotka-custom-order'); ?></p>
                        
                        <div id="pco-categories-sortable" class="pco-categories-grid">
                            <?php foreach ($categories as $id => $category): ?>
                                <div class="pco-category-item" data-id="<?php echo esc_attr($id); ?>">
                                    <div class="pco-category-header">
                                        <span class="pco-category-drag-handle">⋮⋮</span>
                                        <strong><?php echo esc_html($category['name']); ?></strong>
                                        <span class="pco-category-status <?php echo $category['enabled'] ? 'enabled' : 'disabled'; ?>">
                                            <?php echo $category['enabled'] ? __('Włączona', 'papierotka-custom-order') : __('Wyłączona', 'papierotka-custom-order'); ?>
                                        </span>
                                    </div>
                                    
                                    <div class="pco-category-details">
                                        <p><strong><?php _e('ID:', 'papierotka-custom-order'); ?></strong> <?php echo esc_html($id); ?></p>
                                        <p><strong><?php _e('Kolejność:', 'papierotka-custom-order'); ?></strong> <?php echo esc_html($category['order']); ?></p>
                                    </div>
                                    
                                    <div class="pco-category-actions">
                                        <button type="button" class="button button-small pco-edit-category" data-id="<?php echo esc_attr($id); ?>">
                                            <?php _e('Edytuj', 'papierotka-custom-order'); ?>
                                        </button>
                                        <button type="button" class="button button-small button-link-delete pco-delete-category" data-id="<?php echo esc_attr($id); ?>">
                                            <?php _e('Usuń', 'papierotka-custom-order'); ?>
                                        </button>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Modal do edycji kategorii -->
        <div id="pco-edit-category-modal" class="pco-modal" style="display: none;">
            <div class="pco-modal-content">
                <div class="pco-modal-header">
                    <h3><?php _e('Edytuj kategorię', 'papierotka-custom-order'); ?></h3>
                    <button type="button" class="pco-modal-close">&times;</button>
                </div>
                
                <form id="pco-edit-category-form">
                    <table class="form-table">
                        <tr>
                            <th scope="row">
                                <label for="edit_category_name"><?php _e('Nazwa kategorii', 'papierotka-custom-order'); ?></label>
                            </th>
                            <td>
                                <input type="text" id="edit_category_name" name="category_name" class="regular-text" required>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label for="edit_category_enabled"><?php _e('Status', 'papierotka-custom-order'); ?></label>
                            </th>
                            <td>
                                <label>
                                    <input type="checkbox" id="edit_category_enabled" name="category_enabled" value="1">
                                    <?php _e('Kategoria włączona', 'papierotka-custom-order'); ?>
                                </label>
                            </td>
                        </tr>
                    </table>
                    
                    <input type="hidden" id="edit_category_id" name="category_id">
                    
                    <div class="pco-modal-footer">
                        <button type="button" class="button pco-modal-close"><?php _e('Anuluj', 'papierotka-custom-order'); ?></button>
                        <button type="submit" class="button button-primary"><?php _e('Zapisz zmiany', 'papierotka-custom-order'); ?></button>
                    </div>
                </form>
            </div>
        </div>
        <?php
    }
    
    /**
     * AJAX: Zapisanie kategorii
     */
    public function ajax_save_category() {
        check_ajax_referer('pco_categories_nonce', 'nonce');
        
        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(array('message' => __('Brak uprawnień', 'papierotka-custom-order')));
        }
        
        $category_id = sanitize_text_field($_POST['category_id']);
        $category_name = sanitize_text_field($_POST['category_name']);
        $category_enabled = isset($_POST['category_enabled']) && $_POST['category_enabled'] === '1';
        
        if (empty($category_id) || empty($category_name)) {
            wp_send_json_error(array('message' => __('ID i nazwa kategorii są wymagane', 'papierotka-custom-order')));
        }
        
        // Sprawdzenie czy to edycja czy dodawanie
        $is_edit = isset($_POST['is_edit']) && $_POST['is_edit'] === '1';
        
        if ($is_edit) {
            $result = $this->categories_manager->update_category($category_id, array(
                'name' => $category_name,
                'enabled' => $category_enabled
            ));
        } else {
            $result = $this->categories_manager->add_category($category_id, $category_name, $category_enabled);
        }
        
        if (is_wp_error($result)) {
            wp_send_json_error(array('message' => $result->get_error_message()));
        }
        
        wp_send_json_success(array(
            'message' => $is_edit ? __('Kategoria została zaktualizowana', 'papierotka-custom-order') : __('Kategoria została dodana', 'papierotka-custom-order')
        ));
    }
    
    /**
     * AJAX: Usunięcie kategorii
     */
    public function ajax_delete_category() {
        check_ajax_referer('pco_categories_nonce', 'nonce');
        
        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(array('message' => __('Brak uprawnień', 'papierotka-custom-order')));
        }
        
        $category_id = sanitize_text_field($_POST['category_id']);
        
        if (empty($category_id)) {
            wp_send_json_error(array('message' => __('ID kategorii jest wymagane', 'papierotka-custom-order')));
        }
        
        $result = $this->categories_manager->delete_category($category_id);
        
        if (is_wp_error($result)) {
            wp_send_json_error(array('message' => $result->get_error_message()));
        }
        
        wp_send_json_success(array('message' => __('Kategoria została usunięta', 'papierotka-custom-order')));
    }
    
    /**
     * AJAX: Aktualizacja kolejności kategorii
     */
    public function ajax_update_categories_order() {
        check_ajax_referer('pco_categories_nonce', 'nonce');
        
        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(array('message' => __('Brak uprawnień', 'papierotka-custom-order')));
        }
        
        $order_data = array();
        if (isset($_POST['order']) && is_array($_POST['order'])) {
            foreach ($_POST['order'] as $index => $category_id) {
                $order_data[sanitize_text_field($category_id)] = $index + 1;
            }
        }
        
        $result = $this->categories_manager->update_categories_order($order_data);
        
        if (is_wp_error($result)) {
            wp_send_json_error(array('message' => $result->get_error_message()));
        }
        
        wp_send_json_success(array('message' => __('Kolejność kategorii została zaktualizowana', 'papierotka-custom-order')));
    }
}
