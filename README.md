# Papierotka Custom Order

Wtyczka WordPress do obsługi niestandardowych zamówień zaproszeń ślubnych dla sklepu WooCommerce.

## Opis

Wtyczka Papierotka Custom Order umożliwia klientom personalizację zaproszeń ślubnych poprzez wybór różnych opcji dostosowanych do ich potrzeb. Wtyczka oferuje:

- **Dynamiczne kategorie opcji** - łatwe zarządzanie przez panel administracyjny
- **Wizualny konstruktor formularzy** - tworzenie niestandardowych formularzy zamówień
- **Edytowalne szablony e-mail** - personalizacja powiadomień
- **System zarządzania zamówieniami** - kompleksowa obsługa zamówień
- **Responsywny design** - dostosowany do wszystkich urządzeń

Wtyczka dynamicznie aktualizuje cenę w zależności od wybranych opcji i umożliwia złożenie zamówienia bez standardowego procesu zakupowego WooCommerce.

## Instalacja

1. Skopiuj folder `papierotka-custom-order` do katalogu `/wp-content/plugins/` na serwerze WordPress
2. Aktywuj wtyczkę w panelu administracyjnym WordPress (Wtyczki → Zainstalowane wtyczki)
3. Przejdź do Ustawienia → Bezpośrednie linki i kliknij "Zapisz zmiany", aby odświeżyć przepisywanie URL

## Konfiguracja

### 1. Zarządzanie kategoriami opcji (NOWOŚĆ v1.6.0)

**Przejdź do WooCommerce → Kategorie opcji** aby zarządzać kategoriami:

1. **Dodawanie kategorii:**
   - Wypełnij ID kategorii (tylko małe litery, cyfry, podkreślniki)
   - Podaj nazwę wyświetlaną użytkownikom
   - Ustaw status (włączona/wyłączona)
   - Kliknij "Dodaj kategorię"

2. **Edycja kategorii:**
   - Kliknij "Edytuj" przy wybranej kategorii
   - Zmień nazwę lub status
   - Zapisz zmiany

3. **Zmiana kolejności:**
   - Przeciągnij kategorie aby zmienić kolejność wyświetlania

4. **Usuwanie kategorii:**
   - Kliknij "Usuń" i potwierdź operację

### 2. Konfiguracja produktu

1. Edytuj produkt w WooCommerce, dla którego chcesz włączyć niestandardowy formularz
2. W sekcji danych produktu zaznacz opcję "Włącz niestandardowy formularz zamówienia"
3. Dla każdej dostępnej kategorii:
   - Zaznacz "Włącz kategorię" jeśli chcesz ją udostępnić
   - Wybierz tryb: pojedynczy wybór lub wielokrotny (multiselect)
   - Dodaj opcje w formacie "Nazwa opcji | Cena"
4. Zapisz produkt

### Przykład konfiguracji opcji:
```
Biała | 5.00
Kremowa | 7.50
Złota | 10.00
Srebrna | 12.00
```

## Struktura plików

- `papierotka-custom-order.php` - Główny plik wtyczki
- `includes/`
  - `class-categories-manager.php` - **NOWOŚĆ** Zarządzanie kategoriami opcji
  - `class-product-fields.php` - Zarządza polami produktu i wyświetlaniem opcji
  - `class-direct-order-form.php` - Obsługuje formularz zamówienia
  - `class-form-builder.php` - Konstruktor formularzy
  - `class-form-renderer.php` - Renderowanie formularzy
  - `class-email-templates.php` - Zarządzanie szablonami e-mail
  - `class-order-manager.php` - System zarządzania zamówieniami
- `admin/`
  - `class-categories-admin.php` - **NOWOŚĆ** Panel administracyjny kategorii
- `assets/`
  - `js/papierotka-custom-order.js` - Obsługuje dynamiczną aktualizację cen
  - `js/categories-admin.js` - **NOWOŚĆ** JavaScript dla panelu kategorii
  - `css/papierotka-custom-order.css` - Style dla formularza
  - `css/categories-admin.css` - **NOWOŚĆ** Style dla panelu kategorii
- `templates/`
  - `order-form-page.php` - Szablon strony formularza zamówienia
  - `order-confirmation-page.php` - Szablon strony potwierdzenia zamówienia

## Szczegółowy opis funkcjonalności

### 1. System zarządzania kategoriami opcji (v1.6.0)

**Lokalizacja:** WooCommerce → Kategorie opcji

Nowy dynamiczny system umożliwia:
- **Dodawanie kategorii** - bez edycji kodu, przez panel administracyjny
- **Edycja nazw** - zmiana nazw wyświetlanych klientom
- **Włączanie/wyłączanie** - tymczasowe ukrywanie kategorii bez usuwania
- **Zmiana kolejności** - przeciągnij i upuść dla ustalenia kolejności wyświetlania
- **Usuwanie kategorii** - całkowite usunięcie niepotrzebnych kategorii

**Korzyści:**
- Brak konieczności edycji kodu
- Centralne zarządzanie wszystkimi kategoriami
- Automatyczna synchronizacja z formularzami produktów
- Kompatybilność wsteczna z istniejącymi produktami

### 2. Dynamiczna aktualizacja cen

Wtyczka automatycznie aktualizuje ceny w czasie rzeczywistym, gdy klient:
- Zmienia ilość zamawianych zaproszeń
- Wybiera różne opcje personalizacji
- Dodaje lub usuwa dodatki

Aktualizacja cen odbywa się poprzez AJAX, bez przeładowania strony, co zapewnia płynne doświadczenie użytkownika.

### 3. Niestandardowy formularz zamówienia

Po kliknięciu przycisku "Złóż zamówienie", klient jest przekierowywany do niestandardowego formularza, gdzie może podać:
- Dane osobowe (imię, nazwisko, email, telefon)
- Szczegóły ślubu (data, miejsce)
- Dodatkowe informacje
- Załączyć listę gości

### 4. Konstruktor formularzy

Od wersji 1.5.0 dostępny jest wizualny konstruktor formularzy w panelu administracyjnym:

1. Przejdź do **WooCommerce → Konstruktor Formularza**
2. Przeciągnij pola z panelu narzędzi do obszaru formularza
3. Organizuj pola w sekcje i edytuj ich właściwości
4. Konfiguruj:
   - Etykiety i placeholdery pól
   - Pola wymagane i opcjonalne
   - Walidację danych
   - Klasy CSS dla stylowania
   - Opcje dla pól select i radio
5. Podglądaj formularz przed zapisaniem
6. Zapisz konfigurację

#### Dostępne typy pól:
- **Tekst** - podstawowe pole tekstowe
- **E-mail** - pole z walidacją adresu e-mail
- **Telefon** - pole na numer telefonu
- **Data** - selektor daty
- **Czas** - selektor czasu
- **Obszar tekstowy** - wieloliniowe pole tekstowe
- **Lista rozwijana** - pole select z opcjami
- **Pole wyboru** - checkbox
- **Przycisk opcji** - radio button z opcjami
- **Plik** - upload pliku z ograniczeniami typów

### 5. Powiadomienia email

Po złożeniu zamówienia, wtyczka automatycznie wysyła powiadomienia:
- Do administratora sklepu - zawierające wszystkie szczegóły zamówienia
- Do klienta - potwierdzenie przyjęcia zamówienia

#### Edycja szablonów e-mail

Od wersji 1.4.0 dostępna jest możliwość edycji szablonów e-mail w panelu administracyjnym:

1. Przejdź do **WooCommerce → Powiadomienia E-mail**
2. Edytuj szablony dla administratora i klienta
3. Używaj zmiennych dynamicznych takich jak:
   - `{order_id}` - numer zamówienia
   - `{customer_name}` - imię i nazwisko klienta
   - `{product_name}` - nazwa produktu
   - `{total_price}` - cena łączna
   - `{wedding_date}` - data ślubu
   - i wiele innych...
4. Podglądaj e-maile przed zapisaniem
5. Włączaj/wyłączaj poszczególne typy powiadomień

### 6. Bezpieczeństwo

Wtyczka implementuje następujące mechanizmy bezpieczeństwa:
- Weryfikacja nonce dla wszystkich formularzy
- Sanityzacja danych wejściowych
- Walidacja formularzy po stronie serwera

## Debugowanie

Wtyczka zawiera funkcję debugowania, którą można włączyć, ustawiając stałą `PCO_DEBUG` na `true` w pliku głównym wtyczki.

```php
define('PCO_DEBUG', true);
```

Logi debugowania są zapisywane w pliku `debug.log` w katalogu WordPress.

### Typowe problemy i rozwiązania

1. **Problem**: Ceny nie aktualizują się dynamicznie
   **Rozwiązanie**: Sprawdź, czy skrypt JavaScript jest poprawnie ładowany i czy nie ma błędów w konsoli przeglądarki.

2. **Problem**: Formularz zamówienia nie wyświetla się
   **Rozwiązanie**: Odśwież przepisywanie URL w ustawieniach WordPress (Ustawienia → Bezpośrednie linki).

3. **Problem**: Powiadomienia email nie są wysyłane
   **Rozwiązanie**: Sprawdź konfigurację SMTP w WordPress lub zainstaluj wtyczkę do obsługi email (np. WP Mail SMTP).

4. **Problem**: Przekierowanie do formularza zamówienia nie działa w trybie incognito
   **Rozwiązanie**: Upewnij się, że sesja WooCommerce jest poprawnie inicjalizowana. Od wersji 1.0.3 wtyczka automatycznie inicjalizuje sesję dla niezalogowanych użytkowników.

### Obsługa sesji i przekierowań

Od wersji 1.0.3 wtyczka zawiera ulepszoną obsługę sesji WooCommerce dla niezalogowanych użytkowników:

1. **Automatyczna inicjalizacja sesji** - Wtyczka automatycznie inicjalizuje sesję WooCommerce dla niezalogowanych użytkowników przed zapisem danych.
2. **Rozszerzone debugowanie** - W trybie debugowania zapisywane są szczegółowe informacje o stanie sesji i przekierowaniach.
3. **Automatyczne odświeżanie reguł przepisywania URL** - W trybie debugowania reguły przepisywania URL są automatycznie odświeżane przy każdym ładowaniu wtyczki.

## Rozszerzanie wtyczki

### Zarządzanie kategoriami opcji (v1.6.0+)

**Nowy sposób (zalecany):**
Użyj panelu administracyjnego **WooCommerce → Kategorie opcji** - nie wymaga edycji kodu.

**Programistyczny dostęp do API kategorii:**

```php
// Pobieranie instancji managera kategorii
$categories_manager = PCO_Categories_Manager::get_instance();

// Dodawanie nowej kategorii
$result = $categories_manager->add_category('nowa_kategoria', 'Nazwa nowej kategorii', true);

// Pobieranie wszystkich kategorii
$categories = $categories_manager->get_categories();

// Pobieranie tylko włączonych kategorii
$enabled_categories = $categories_manager->get_categories(true);

// Sprawdzanie czy kategoria istnieje
$exists = $categories_manager->category_exists('nowa_kategoria');
```

### Stary sposób (przestarzały)

⚠️ **Uwaga:** Poniższy sposób jest przestarzały od wersji 1.6.0. Użyj panelu administracyjnego.

### Modyfikacja formularza zamówienia

Formularz zamówienia jest generowany w metodzie `generate_form()` w pliku `class-direct-order-form.php`. Aby dodać nowe pola do formularza, zmodyfikuj tę metodę.

## Historia zmian

### v1.6.0 (2025-01-XX) - NOWOŚĆ
- ✅ **Dynamiczny system zarządzania kategoriami opcji**
- ✅ Panel administracyjny: WooCommerce → Kategorie opcji
- ✅ Dodawanie/edycja/usuwanie kategorii bez edycji kodu
- ✅ Zmiana kolejności wyświetlania (drag & drop)
- ✅ Włączanie/wyłączanie kategorii
- ✅ Kompatybilność wsteczna z istniejącymi produktami
- ✅ Centralne API zarządzania kategoriami

### v1.5.0 (2025-01-XX)
- Wizualny konstruktor formularzy w panelu administracyjnym

### v1.4.0 (2025-01-XX)
- Edytowalny system szablonów e-mail w panelu administracyjnym

Zobacz plik [CHANGELOG.md](CHANGELOG.md) dla pełnej historii zmian.

## Wsparcie i rozwój

W przypadku problemów lub pytań dotyczących wtyczki, skontaktuj się z autorem.

## Autor

Papierotka - Zaproszenia ślubne
