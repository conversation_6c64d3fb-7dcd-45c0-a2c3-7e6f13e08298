/**
 * Style CSS dla Dashboard wtyczki Papierotka Custom Order
 */

.pco-dashboard {
    max-width: 1200px;
}

.pco-dashboard h1 {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 30px;
}

.pco-dashboard h1 .dashicons {
    font-size: 32px;
    width: 32px;
    height: 32px;
    color: #0073aa;
}

.pco-dashboard-content {
    display: grid;
    gap: 30px;
}

/* Statystyki */
.pco-stats-section h2 {
    margin-bottom: 20px;
    color: #23282d;
    border-bottom: 2px solid #0073aa;
    padding-bottom: 10px;
}

.pco-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.pco-stat-card {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.pco-stat-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.pco-stat-icon {
    background: #0073aa;
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pco-stat-icon .dashicons {
    font-size: 24px;
    width: 24px;
    height: 24px;
}

.pco-stat-content h3 {
    margin: 0 0 5px 0;
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.pco-stat-number {
    font-size: 28px;
    font-weight: bold;
    color: #0073aa;
}

/* Ostatnie zamówienia */
.pco-recent-orders-section h2 {
    margin-bottom: 20px;
    color: #23282d;
    border-bottom: 2px solid #0073aa;
    padding-bottom: 10px;
}

.pco-recent-orders-container {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 8px;
    overflow: hidden;
}

.pco-loading {
    padding: 40px;
    text-align: center;
    color: #666;
}

.pco-loading .spinner {
    float: none;
    margin-right: 10px;
}

.pco-view-all {
    margin-top: 15px;
    text-align: center;
}

/* Szybkie linki */
.pco-quick-links-section h2 {
    margin-bottom: 20px;
    color: #23282d;
    border-bottom: 2px solid #0073aa;
    padding-bottom: 10px;
}

.pco-quick-links-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.pco-quick-link {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 8px;
    padding: 20px;
    text-decoration: none;
    color: #23282d;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: all 0.3s ease;
}

.pco-quick-link:hover {
    background: #f6f7f7;
    border-color: #0073aa;
    color: #0073aa;
    text-decoration: none;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.pco-quick-link .dashicons {
    font-size: 20px;
    width: 20px;
    height: 20px;
}

.pco-quick-link span:last-child {
    font-weight: 500;
}

/* Status konfiguracji */
.pco-config-status-section h2 {
    margin-bottom: 20px;
    color: #23282d;
    border-bottom: 2px solid #0073aa;
    padding-bottom: 10px;
}

.pco-config-status {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 8px;
    padding: 20px;
}

.pco-config-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f1;
}

.pco-config-item:last-child {
    border-bottom: none;
}

.pco-config-item.configured .dashicons {
    color: #00a32a;
}

.pco-config-item.not-configured .dashicons {
    color: #dba617;
}

.pco-config-item .dashicons {
    font-size: 18px;
    width: 18px;
    height: 18px;
}

.pco-config-item span:nth-child(2) {
    flex: 1;
    font-weight: 500;
}

.pco-config-item .button {
    margin-left: auto;
}

.pco-config-item .description {
    margin-left: auto;
    font-style: italic;
    color: #666;
    font-size: 12px;
}

/* Responsywność */
@media (max-width: 768px) {
    .pco-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .pco-quick-links-grid {
        grid-template-columns: 1fr;
    }
    
    .pco-stat-card {
        padding: 15px;
    }
    
    .pco-quick-link {
        padding: 15px;
    }
    
    .pco-config-item {
        flex-wrap: wrap;
        gap: 8px;
    }
    
    .pco-config-item .button,
    .pco-config-item .description {
        margin-left: 0;
        margin-top: 5px;
    }
}

/* Animacje ładowania */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pco-stat-card,
.pco-quick-link,
.pco-config-item {
    animation: fadeIn 0.5s ease-out;
}

/* Tabela zamówień */
.pco-recent-orders-container table {
    margin: 0;
}

.pco-recent-orders-container .wp-list-table th,
.pco-recent-orders-container .wp-list-table td {
    padding: 12px;
}

.pco-recent-orders-container .wp-list-table th {
    background: #f6f7f7;
    font-weight: 600;
}

/* Status zamówienia */
.pco-order-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.pco-order-status.pending {
    background: #fff3cd;
    color: #856404;
}

.pco-order-status.processing {
    background: #d1ecf1;
    color: #0c5460;
}

.pco-order-status.completed {
    background: #d4edda;
    color: #155724;
}

.pco-order-status.cancelled {
    background: #f8d7da;
    color: #721c24;
}
